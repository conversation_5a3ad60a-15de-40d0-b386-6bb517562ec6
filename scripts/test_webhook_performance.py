#!/usr/bin/env python3
"""
Скрипт для тестирования производительности вебхуков
Измеряет время ответа до и после оптимизаций
"""
import asyncio
import aiohttp
import time
import json
import statistics
from typing import List, Dict
from dataclasses import dataclass

@dataclass
class TestResult:
    response_time: float
    status_code: int
    success: bool
    error: str = None

class WebhookPerformanceTester:
    def __init__(self, webhook_url: str = "https://edubot.schoolpro.kz/webhook"):
        self.webhook_url = webhook_url
        self.results: List[TestResult] = []
    
    def create_test_update(self, user_id: int, message: str = "/start") -> dict:
        """Создает тестовое обновление от Telegram"""
        return {
            "update_id": int(time.time() * 1000),
            "message": {
                "message_id": int(time.time()),
                "from": {
                    "id": user_id,
                    "is_bot": False,
                    "first_name": f"TestUser{user_id}",
                    "username": f"testuser{user_id}",
                    "language_code": "ru"
                },
                "chat": {
                    "id": user_id,
                    "first_name": f"TestUser{user_id}",
                    "username": f"testuser{user_id}",
                    "type": "private"
                },
                "date": int(time.time()),
                "text": message
            }
        }
    
    async def send_single_request(self, session: aiohttp.ClientSession, user_id: int) -> TestResult:
        """Отправляет один тестовый запрос"""
        start_time = time.time()
        
        try:
            test_update = self.create_test_update(user_id)
            
            async with session.post(
                self.webhook_url,
                json=test_update,
                headers={'Content-Type': 'application/json'},
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                response_time = time.time() - start_time
                
                return TestResult(
                    response_time=response_time,
                    status_code=response.status,
                    success=response.status == 200
                )
                
        except Exception as e:
            response_time = time.time() - start_time
            return TestResult(
                response_time=response_time,
                status_code=0,
                success=False,
                error=str(e)
            )
    
    async def run_performance_test(self, num_requests: int = 50, concurrent: int = 10):
        """Запускает тест производительности"""
        print(f"🚀 Запуск теста производительности:")
        print(f"   📊 Запросов: {num_requests}")
        print(f"   🔄 Одновременно: {concurrent}")
        print(f"   🌐 URL: {self.webhook_url}")
        print()
        
        connector = aiohttp.TCPConnector(limit=concurrent * 2)
        timeout = aiohttp.ClientTimeout(total=10)
        
        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            # Создаем задачи
            tasks = []
            for i in range(num_requests):
                user_id = 1000000 + i  # Уникальные ID для тестов
                task = asyncio.create_task(self.send_single_request(session, user_id))
                tasks.append(task)
                
                # Ограничиваем количество одновременных запросов
                if len(tasks) >= concurrent:
                    # Ждем завершения части задач
                    completed_tasks = await asyncio.gather(*tasks[:concurrent])
                    self.results.extend(completed_tasks)
                    tasks = tasks[concurrent:]
                    
                    # Небольшая пауза между пачками
                    await asyncio.sleep(0.1)
            
            # Обрабатываем оставшиеся задачи
            if tasks:
                completed_tasks = await asyncio.gather(*tasks)
                self.results.extend(completed_tasks)
    
    def analyze_results(self) -> Dict:
        """Анализирует результаты тестирования"""
        if not self.results:
            return {}
        
        successful_results = [r for r in self.results if r.success]
        failed_results = [r for r in self.results if not r.success]
        
        if successful_results:
            response_times = [r.response_time for r in successful_results]
            
            analysis = {
                'total_requests': len(self.results),
                'successful_requests': len(successful_results),
                'failed_requests': len(failed_results),
                'success_rate': len(successful_results) / len(self.results) * 100,
                'avg_response_time': statistics.mean(response_times),
                'min_response_time': min(response_times),
                'max_response_time': max(response_times),
                'median_response_time': statistics.median(response_times),
                'p95_response_time': sorted(response_times)[int(len(response_times) * 0.95)],
                'p99_response_time': sorted(response_times)[int(len(response_times) * 0.99)],
            }
        else:
            analysis = {
                'total_requests': len(self.results),
                'successful_requests': 0,
                'failed_requests': len(failed_results),
                'success_rate': 0,
            }
        
        return analysis
    
    def print_results(self):
        """Выводит результаты тестирования"""
        analysis = self.analyze_results()
        
        if not analysis:
            print("❌ Нет результатов для анализа")
            return
        
        print("📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:")
        print("=" * 50)
        print(f"📈 Всего запросов: {analysis['total_requests']}")
        print(f"✅ Успешных: {analysis['successful_requests']}")
        print(f"❌ Неудачных: {analysis['failed_requests']}")
        print(f"📊 Успешность: {analysis['success_rate']:.1f}%")
        
        if analysis['successful_requests'] > 0:
            print()
            print("⏱️  ВРЕМЯ ОТВЕТА:")
            print(f"   Среднее: {analysis['avg_response_time']*1000:.0f}ms")
            print(f"   Минимум: {analysis['min_response_time']*1000:.0f}ms")
            print(f"   Максимум: {analysis['max_response_time']*1000:.0f}ms")
            print(f"   Медиана: {analysis['median_response_time']*1000:.0f}ms")
            print(f"   95-й процентиль: {analysis['p95_response_time']*1000:.0f}ms")
            print(f"   99-й процентиль: {analysis['p99_response_time']*1000:.0f}ms")
            
            # Оценка производительности
            avg_ms = analysis['avg_response_time'] * 1000
            if avg_ms < 200:
                print(f"🚀 ОТЛИЧНО! Среднее время < 200ms")
            elif avg_ms < 500:
                print(f"✅ ХОРОШО! Среднее время < 500ms")
            elif avg_ms < 1000:
                print(f"⚠️  ПРИЕМЛЕМО. Среднее время < 1s")
            else:
                print(f"🐌 МЕДЛЕННО! Среднее время > 1s")
        
        # Показываем ошибки
        failed_results = [r for r in self.results if not r.success]
        if failed_results:
            print()
            print("❌ ОШИБКИ:")
            error_counts = {}
            for result in failed_results:
                error = result.error or f"HTTP {result.status_code}"
                error_counts[error] = error_counts.get(error, 0) + 1
            
            for error, count in error_counts.items():
                print(f"   {error}: {count} раз")

async def main():
    """Основная функция тестирования"""
    tester = WebhookPerformanceTester()
    
    print("🔧 Тестирование производительности вебхуков")
    print("=" * 50)
    
    # Запускаем тест
    await tester.run_performance_test(num_requests=30, concurrent=5)
    
    # Выводим результаты
    tester.print_results()

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
Скрипт для мониторинга производительности бота в реальном времени
"""
import asyncio
import aiohttp
import time
import json
from datetime import datetime
from typing import Dict, List

class PerformanceMonitor:
    def __init__(self, webhook_url: str = "https://edubot.schoolpro.kz/webhook"):
        self.webhook_url = webhook_url
        self.health_url = "https://edubot.schoolpro.kz/health"
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'response_times': [],
            'errors': {},
            'start_time': time.time()
        }
    
    def create_test_update(self, user_id: int) -> dict:
        """Создает тестовое обновление"""
        return {
            "update_id": int(time.time() * 1000),
            "message": {
                "message_id": int(time.time()),
                "from": {
                    "id": user_id,
                    "is_bot": False,
                    "first_name": f"Monitor{user_id}",
                    "username": f"monitor{user_id}",
                    "language_code": "ru"
                },
                "chat": {
                    "id": user_id,
                    "first_name": f"Monitor{user_id}",
                    "username": f"monitor{user_id}",
                    "type": "private"
                },
                "date": int(time.time()),
                "text": "/start"
            }
        }
    
    async def check_health(self, session: aiohttp.ClientSession) -> Dict:
        """Проверяет health endpoint"""
        try:
            start_time = time.time()
            async with session.get(self.health_url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                response_time = time.time() - start_time
                return {
                    'status': 'healthy' if response.status == 200 else 'unhealthy',
                    'response_time': response_time,
                    'status_code': response.status
                }
        except Exception as e:
            return {
                'status': 'error',
                'response_time': 0,
                'error': str(e)
            }
    
    async def test_webhook(self, session: aiohttp.ClientSession, user_id: int) -> Dict:
        """Тестирует webhook"""
        try:
            start_time = time.time()
            test_update = self.create_test_update(user_id)
            
            async with session.post(
                self.webhook_url,
                json=test_update,
                headers={'Content-Type': 'application/json'},
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                response_time = time.time() - start_time
                
                self.stats['total_requests'] += 1
                
                if response.status == 200:
                    self.stats['successful_requests'] += 1
                    self.stats['response_times'].append(response_time)
                    return {
                        'status': 'success',
                        'response_time': response_time,
                        'status_code': response.status
                    }
                else:
                    self.stats['failed_requests'] += 1
                    error_key = f"HTTP_{response.status}"
                    self.stats['errors'][error_key] = self.stats['errors'].get(error_key, 0) + 1
                    return {
                        'status': 'failed',
                        'response_time': response_time,
                        'status_code': response.status
                    }
                    
        except Exception as e:
            self.stats['total_requests'] += 1
            self.stats['failed_requests'] += 1
            error_key = str(type(e).__name__)
            self.stats['errors'][error_key] = self.stats['errors'].get(error_key, 0) + 1
            
            return {
                'status': 'error',
                'response_time': 0,
                'error': str(e)
            }
    
    def get_statistics(self) -> Dict:
        """Получает текущую статистику"""
        if not self.stats['response_times']:
            return {
                'uptime': time.time() - self.stats['start_time'],
                'total_requests': self.stats['total_requests'],
                'success_rate': 0,
                'avg_response_time': 0,
                'errors': self.stats['errors']
            }
        
        response_times = self.stats['response_times']
        success_rate = (self.stats['successful_requests'] / self.stats['total_requests'] * 100) if self.stats['total_requests'] > 0 else 0
        
        return {
            'uptime': time.time() - self.stats['start_time'],
            'total_requests': self.stats['total_requests'],
            'successful_requests': self.stats['successful_requests'],
            'failed_requests': self.stats['failed_requests'],
            'success_rate': round(success_rate, 2),
            'avg_response_time': round(sum(response_times) / len(response_times) * 1000, 0),
            'min_response_time': round(min(response_times) * 1000, 0),
            'max_response_time': round(max(response_times) * 1000, 0),
            'last_10_avg': round(sum(response_times[-10:]) / min(len(response_times), 10) * 1000, 0),
            'errors': self.stats['errors']
        }
    
    def print_status(self, health_status: Dict, webhook_status: Dict):
        """Выводит текущий статус"""
        stats = self.get_statistics()
        current_time = datetime.now().strftime("%H:%M:%S")
        
        # Очищаем экран (работает в большинстве терминалов)
        print("\033[2J\033[H", end="")
        
        print(f"🔍 МОНИТОРИНГ ПРОИЗВОДИТЕЛЬНОСТИ БОТА - {current_time}")
        print("=" * 60)
        
        # Health status
        health_emoji = "✅" if health_status['status'] == 'healthy' else "❌"
        print(f"{health_emoji} Health: {health_status['status']} ({health_status.get('response_time', 0)*1000:.0f}ms)")
        
        # Webhook status
        webhook_emoji = "✅" if webhook_status['status'] == 'success' else "❌"
        print(f"{webhook_emoji} Webhook: {webhook_status['status']} ({webhook_status.get('response_time', 0)*1000:.0f}ms)")
        
        print()
        print("📊 СТАТИСТИКА:")
        print(f"   ⏱️  Время работы: {stats['uptime']:.0f}s")
        print(f"   📈 Всего запросов: {stats['total_requests']}")
        print(f"   ✅ Успешных: {stats['successful_requests']}")
        print(f"   ❌ Неудачных: {stats['failed_requests']}")
        print(f"   📊 Успешность: {stats['success_rate']}%")
        
        if stats['avg_response_time'] > 0:
            print()
            print("⏱️  ВРЕМЯ ОТВЕТА:")
            print(f"   Среднее: {stats['avg_response_time']}ms")
            print(f"   Минимум: {stats['min_response_time']}ms")
            print(f"   Максимум: {stats['max_response_time']}ms")
            print(f"   Последние 10: {stats['last_10_avg']}ms")
            
            # Оценка производительности
            if stats['avg_response_time'] < 200:
                print("   🚀 ОТЛИЧНО!")
            elif stats['avg_response_time'] < 500:
                print("   ✅ ХОРОШО")
            elif stats['avg_response_time'] < 1000:
                print("   ⚠️  ПРИЕМЛЕМО")
            else:
                print("   🐌 МЕДЛЕННО")
        
        if stats['errors']:
            print()
            print("❌ ОШИБКИ:")
            for error, count in stats['errors'].items():
                print(f"   {error}: {count}")
        
        print()
        print("⚠️  Нажмите Ctrl+C для остановки")
    
    async def run_monitoring(self, interval: int = 5):
        """Запускает мониторинг"""
        print("🚀 Запуск мониторинга производительности...")
        print(f"📊 Интервал проверки: {interval} секунд")
        print(f"🌐 Webhook URL: {self.webhook_url}")
        print(f"🏥 Health URL: {self.health_url}")
        print()
        
        connector = aiohttp.TCPConnector(limit=10)
        timeout = aiohttp.ClientTimeout(total=10)
        
        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            try:
                while True:
                    # Проверяем health
                    health_status = await self.check_health(session)
                    
                    # Тестируем webhook
                    webhook_status = await self.test_webhook(session, 999999)
                    
                    # Выводим статус
                    self.print_status(health_status, webhook_status)
                    
                    # Ждем следующую проверку
                    await asyncio.sleep(interval)
                    
            except KeyboardInterrupt:
                print("\n🛑 Мониторинг остановлен")
                
                # Финальная статистика
                final_stats = self.get_statistics()
                print("\n📊 ФИНАЛЬНАЯ СТАТИСТИКА:")
                print(f"   Время работы: {final_stats['uptime']:.0f}s")
                print(f"   Всего запросов: {final_stats['total_requests']}")
                print(f"   Успешность: {final_stats['success_rate']}%")
                if final_stats['avg_response_time'] > 0:
                    print(f"   Среднее время ответа: {final_stats['avg_response_time']}ms")

async def main():
    """Основная функция"""
    monitor = PerformanceMonitor()
    await monitor.run_monitoring(interval=3)  # Проверка каждые 3 секунды

if __name__ == "__main__":
    asyncio.run(main())

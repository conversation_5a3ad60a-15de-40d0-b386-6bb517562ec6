"""
Middleware для мониторинга производительности вебхуков
"""
import time
import logging
from typing import Callable, Dict, Any, Awaitable
from aiogram import BaseMiddleware
from aiogram.types import Message, CallbackQuery

class PerformanceMiddleware(BaseMiddleware):
    """Middleware для измерения времени обработки запросов"""
    
    def __init__(self, log_slow_requests: bool = True, slow_threshold: float = 1.0):
        self.log_slow_requests = log_slow_requests
        self.slow_threshold = slow_threshold  # Порог медленных запросов в секундах
        
    async def __call__(
        self,
        handler: Callable[[Message | CallbackQuery, Dict[str, Any]], Awaitable[Any]],
        event: Message | CallbackQuery,
        data: Dict[str, Any]
    ) -> Any:
        start_time = time.time()
        
        try:
            # Выполняем обработчик
            result = await handler(event, data)
            
            # Измеряем время выполнения
            execution_time = time.time() - start_time
            
            # Логируем медленные запросы
            if self.log_slow_requests and execution_time > self.slow_threshold:
                user_id = event.from_user.id if event.from_user else "unknown"
                event_type = "message" if isinstance(event, Message) else "callback"
                
                logging.warning(
                    f"🐌 МЕДЛЕННЫЙ ЗАПРОС: {execution_time:.2f}s | "
                    f"User: {user_id} | Type: {event_type} | "
                    f"Role: {data.get('user_role', 'unknown')}"
                )
            
            # Добавляем время выполнения в данные для других middleware
            data["execution_time"] = execution_time
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            logging.error(f"❌ ОШИБКА ОБРАБОТКИ ({execution_time:.2f}s): {e}")
            raise

"""
Менеджер кэширования для ускорения работы бота
"""
import json
import logging
from typing import Any, Optional, Dict, List
from utils.redis_manager import RedisManager
from utils.config import REDIS_ENABLED

class CacheManager:
    """Менеджер кэширования с поддержкой Redis и fallback на память"""
    
    def __init__(self):
        self.redis_manager = RedisManager() if REDIS_ENABLED else None
        self.memory_cache: Dict[str, Any] = {}  # Fallback кэш в памяти
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0
        }
    
    async def get(self, key: str) -> Optional[Any]:
        """Получить значение из кэша"""
        try:
            # Пробуем Redis
            if self.redis_manager and self.redis_manager.connected:
                value = await self.redis_manager.get(key)
                if value is not None:
                    self.cache_stats['hits'] += 1
                    return json.loads(value) if isinstance(value, str) else value
            
            # Fallback на память
            if key in self.memory_cache:
                self.cache_stats['hits'] += 1
                return self.memory_cache[key]
            
            self.cache_stats['misses'] += 1
            return None
            
        except Exception as e:
            logging.error(f"❌ Ошибка получения из кэша {key}: {e}")
            self.cache_stats['misses'] += 1
            return None
    
    async def set(self, key: str, value: Any, ttl: int = 300) -> bool:
        """Установить значение в кэш"""
        try:
            # Пробуем Redis
            if self.redis_manager and self.redis_manager.connected:
                serialized_value = json.dumps(value) if not isinstance(value, str) else value
                success = await self.redis_manager.set(key, serialized_value, ttl)
                if success:
                    self.cache_stats['sets'] += 1
                    return True
            
            # Fallback на память
            self.memory_cache[key] = value
            self.cache_stats['sets'] += 1
            return True
            
        except Exception as e:
            logging.error(f"❌ Ошибка записи в кэш {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Удалить значение из кэша"""
        try:
            success = True
            
            # Удаляем из Redis
            if self.redis_manager and self.redis_manager.connected:
                await self.redis_manager.delete(key)
            
            # Удаляем из памяти
            if key in self.memory_cache:
                del self.memory_cache[key]
            
            return success
            
        except Exception as e:
            logging.error(f"❌ Ошибка удаления из кэша {key}: {e}")
            return False
    
    async def clear_pattern(self, pattern: str) -> int:
        """Очистить кэш по паттерну"""
        try:
            count = 0
            
            # Очищаем Redis
            if self.redis_manager and self.redis_manager.connected:
                keys = await self.redis_manager.keys(pattern)
                if keys:
                    count += await self.redis_manager.delete(*keys)
            
            # Очищаем память
            keys_to_delete = [k for k in self.memory_cache.keys() if pattern.replace('*', '') in k]
            for key in keys_to_delete:
                del self.memory_cache[key]
                count += 1
            
            return count
            
        except Exception as e:
            logging.error(f"❌ Ошибка очистки кэша по паттерну {pattern}: {e}")
            return 0
    
    def get_stats(self) -> Dict[str, Any]:
        """Получить статистику кэша"""
        total_requests = self.cache_stats['hits'] + self.cache_stats['misses']
        hit_rate = (self.cache_stats['hits'] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'hits': self.cache_stats['hits'],
            'misses': self.cache_stats['misses'],
            'sets': self.cache_stats['sets'],
            'hit_rate': round(hit_rate, 2),
            'memory_cache_size': len(self.memory_cache),
            'redis_connected': self.redis_manager.connected if self.redis_manager else False
        }

# Глобальный экземпляр кэш-менеджера
cache_manager = CacheManager()

# Функции-хелперы для удобства
async def get_cached(key: str) -> Optional[Any]:
    """Получить значение из кэша"""
    return await cache_manager.get(key)

async def set_cached(key: str, value: Any, ttl: int = 300) -> bool:
    """Установить значение в кэш"""
    return await cache_manager.set(key, value, ttl)

async def delete_cached(key: str) -> bool:
    """Удалить значение из кэша"""
    return await cache_manager.delete(key)

async def clear_cache_pattern(pattern: str) -> int:
    """Очистить кэш по паттерну"""
    return await cache_manager.clear_pattern(pattern)

# Декоратор для кэширования функций
def cached(ttl: int = 300, key_prefix: str = "func"):
    """Декоратор для кэширования результатов функций"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Создаем ключ кэша
            cache_key = f"{key_prefix}:{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # Пробуем получить из кэша
            cached_result = await get_cached(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Выполняем функцию и кэшируем результат
            result = await func(*args, **kwargs)
            await set_cached(cache_key, result, ttl)
            
            return result
        return wrapper
    return decorator

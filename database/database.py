"""
Конфигурация подключения к базе данных
"""
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from os import getenv
from dotenv import load_dotenv
from .models import Base

load_dotenv()

# Формируем DATABASE_URL из отдельных переменных для избежания дублирования
POSTGRES_USER = getenv("POSTGRES_USER", "telebot_user")
POSTGRES_PASSWORD = getenv("POSTGRES_PASSWORD", "your_secure_password")
POSTGRES_DB = getenv("POSTGRES_DB", "telebot")
POSTGRES_HOST = getenv("POSTGRES_HOST", "postgres")
POSTGRES_PORT = getenv("POSTGRES_PORT", "5432")

DATABASE_URL = f"postgresql+asyncpg://{POSTGRES_USER}:{POSTGRES_PASSWORD}@{POSTGRES_HOST}:{POSTGRES_PORT}/{POSTGRES_DB}"

# Создание движка с оптимизированными настройками
engine = create_async_engine(
    DATABASE_URL,
    echo=False,
    # Настройки пула соединений для высокой производительности
    pool_size=10,          # Базовый размер пула
    max_overflow=20,       # Дополнительные соединения при нагрузке
    pool_timeout=5,        # Таймаут получения соединения из пула
    pool_recycle=3600,     # Переиспользование соединений (1 час)
    pool_pre_ping=True,    # Проверка соединений перед использованием
    # Оптимизации для PostgreSQL
    connect_args={
        "server_settings": {
            "application_name": "telebot_webhook",
            "jit": "off",  # Отключаем JIT для быстрых запросов
        }
    }
)

async_session = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    # Оптимизация сессий
    autoflush=False,  # Отключаем автофлаш для производительности
)


# Функции инициализации базы данных
async def init_database():
    """Инициализация базы данных - создание всех таблиц"""
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    print("✅ База данных инициализирована")


async def close_database():
    """Закрытие соединения с базой данных"""
    await engine.dispose()
    print("🔌 Соединение с базой данных закрыто")


# Функция для получения сессии базы данных
def get_db_session() -> AsyncSession:
    """Получить сессию базы данных"""
    return async_session()
